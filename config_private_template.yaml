# N8N_Builder Enterprise Edition Configuration Template
# This file shows how advanced features would be configured
# (Not included in public repository)

# Application Settings
app:
  name: "N8N_Builder Enterprise Edition"
  version: "1.0.0"
  description: "AI-powered workflow automation with advanced self-healing"
  
# Server Configuration
server:
  host: "127.0.0.1"
  port: 8002
  agui_port: 8003
  dashboard_port: 8081  # Self-Healer dashboard
  log_level: "INFO"
  reload: false

# AI Model Configuration
ai:
  endpoint: "http://localhost:1234/v1"
  model: "mimo-vl-7b-rl"
  max_tokens: 2000
  temperature: 0.7
  timeout: 30

# MCP Research Tool Configuration
mcp_research:
  enabled: true
  timeout: 30
  cache_ttl: 3600
  max_content_length: 2000
  max_results_per_source: 5
  sources:
    official_docs: "https://docs.n8n.io/"
    community_forum: "https://community.n8n.io/"
    github_main: "https://github.com/n8n-io/n8n"
    templates: "https://n8n.io/workflows/"

# Database Configuration (for KnowledgeBase)
database:
  connection_name: "knowledgebase"
  server: "localhost"
  database: "KnowledgeBase"
  trusted_connection: true
  driver: "ODBC Driver 17 for SQL Server"

# Error Handling Configuration
error_handling:
  mode: "advanced"  # Enterprise edition uses Self-Healer
  log_errors: true
  retry_attempts: 3
  retry_delay: 1.0
  self_healer_enabled: true

# Self-Healer Configuration (Enterprise Feature)
self_healer:
  enabled: true
  monitoring:
    check_interval: 5.0
    max_concurrent_sessions: 3
    log_files:
      - "logs/errors.log"
      - "logs/n8n_builder.log"
  
  safety:
    require_validation: true
    auto_rollback_on_failure: true
    max_healing_attempts_per_hour: 10
    max_file_changes: 5
    emergency_stop_threshold: 3
  
  learning:
    enable_learning: true
    feedback_collection: true
    pattern_recognition: true
  
  dashboard:
    enabled: true
    port: 8081
    auto_refresh: 30

# KnowledgeBase Configuration (Enterprise Feature)
knowledge_base:
  enabled: true
  validity_rating_threshold: 0.6
  auto_update_facts: true
  cross_validation: true
  evidence_tracking: true
  
  integration:
    self_healer: true
    workflow_generation: true
    error_resolution: true

# Advanced Analytics (Enterprise Feature)
analytics:
  enabled: true
  metrics_collection: true
  performance_tracking: true
  success_rate_monitoring: true
  predictive_analysis: true

# Optional Features (All available in Enterprise Edition)
optional_features:
  self_healer:
    enabled: true
    reason: "Enterprise Edition feature"
  
  knowledge_base:
    enabled: true
    reason: "Enterprise Edition feature"
  
  advanced_analytics:
    enabled: true
    reason: "Enterprise Edition feature"
  
  predictive_maintenance:
    enabled: true
    reason: "Enterprise Edition feature"

# Enhanced Security (Enterprise Feature)
security:
  cors_enabled: true
  allowed_origins: ["http://localhost:*"]
  rate_limiting: true
  max_requests_per_minute: 100
  authentication_required: false  # Can be enabled for production
  audit_logging: true

# Performance Settings (Enhanced for Enterprise)
performance:
  max_concurrent_requests: 50
  request_timeout: 300
  cache_cleanup_interval: 1800
  advanced_caching: true
  load_balancing: false  # Can be enabled for production

# Backup and Recovery (Enterprise Feature)
backup:
  enabled: true
  backup_directory: "Self_Healer/backups"
  max_backup_age_days: 30
  compress_backups: true
  verify_backups: true
  auto_backup_interval: 24  # hours

# Monitoring and Alerting (Enterprise Feature)
monitoring:
  health_checks: true
  performance_metrics: true
  error_alerting: true
  dashboard_integration: true
  
  alerts:
    email_notifications: false
    webhook_notifications: false
    slack_integration: false
