# Cleanup Duplicate Self-Healer Folder
# This script safely removes the redundant Self-Healer folder (hyphenated)
# while preserving the main Self_Healer folder (underscore)

param(
    [switch]$DryRun,
    [switch]$Execute
)

Write-Host "🧹 Self-Healer Folder Cleanup" -ForegroundColor Green
Write-Host "=============================" -ForegroundColor Green

if (-not $DryRun -and -not $Execute) {
    Write-Host "❌ Please specify either -DryRun or -Execute" -ForegroundColor Red
    Write-Host "Usage: .\cleanup_duplicate_folder.ps1 -DryRun    # Preview what will be deleted"
    Write-Host "       .\cleanup_duplicate_folder.ps1 -Execute   # Execute deletion"
    exit 1
}

$redundantFolder = "Self-Healer"
$mainFolder = "Self_Healer"

# Verify both folders exist
if (-not (Test-Path $redundantFolder)) {
    Write-Host "✅ Redundant folder '$redundantFolder' does not exist - nothing to clean up" -ForegroundColor Green
    exit 0
}

if (-not (Test-Path $mainFolder)) {
    Write-Host "❌ Main folder '$mainFolder' does not exist - aborting for safety" -ForegroundColor Red
    exit 1
}

Write-Host "📁 Found both folders:" -ForegroundColor Cyan
Write-Host "  🗑️ Redundant: $redundantFolder (will be deleted)" -ForegroundColor Yellow
Write-Host "  ✅ Main: $mainFolder (will be preserved)" -ForegroundColor Green

# Analyze content
Write-Host ""
Write-Host "📊 Content Analysis:" -ForegroundColor Cyan

Write-Host "🗑️ Redundant folder contents:" -ForegroundColor Yellow
Get-ChildItem $redundantFolder -Recurse | ForEach-Object {
    $relativePath = $_.FullName.Substring((Get-Location).Path.Length + 1)
    Write-Host "  - $relativePath" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "✅ Main folder structure:" -ForegroundColor Green
Get-ChildItem $mainFolder -Directory | ForEach-Object {
    Write-Host "  + $($_.Name)/" -ForegroundColor Green
}

if ($DryRun) {
    Write-Host ""
    Write-Host "🔍 DRY RUN - Would delete:" -ForegroundColor Yellow
    Write-Host "  🗑️ $redundantFolder and all contents" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "🎯 To execute deletion: .\cleanup_duplicate_folder.ps1 -Execute" -ForegroundColor Cyan
}

if ($Execute) {
    Write-Host ""
    Write-Host "⚠️ EXECUTING DELETION..." -ForegroundColor Red
    Write-Host "Deleting redundant folder: $redundantFolder" -ForegroundColor Yellow
    
    try {
        Remove-Item $redundantFolder -Recurse -Force
        Write-Host "✅ Successfully deleted redundant folder: $redundantFolder" -ForegroundColor Green
        
        # Verify deletion
        if (-not (Test-Path $redundantFolder)) {
            Write-Host "✅ Verification: Folder successfully removed" -ForegroundColor Green
        } else {
            Write-Host "⚠️ Warning: Folder still exists after deletion attempt" -ForegroundColor Yellow
        }
        
        # Verify main folder is intact
        if (Test-Path $mainFolder) {
            Write-Host "✅ Verification: Main folder '$mainFolder' preserved" -ForegroundColor Green
        } else {
            Write-Host "❌ ERROR: Main folder '$mainFolder' was accidentally deleted!" -ForegroundColor Red
        }
        
    } catch {
        Write-Host "❌ Error deleting folder: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
    
    Write-Host ""
    Write-Host "🎉 Cleanup complete!" -ForegroundColor Green
    Write-Host "📁 Your main Self-Healer system remains in: $mainFolder" -ForegroundColor Cyan
}
