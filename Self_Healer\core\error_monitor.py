"""
Error Monitor - Detects and classifies errors from log files and system state.

This module continuously monitors error logs and system state to detect issues
that require healing intervention.
"""

import asyncio
import logging
import time
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set, Any, TYPE_CHECKING

if TYPE_CHECKING:
    import aiohttp
from pathlib import Path
from dataclasses import dataclass
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import re

# Import existing N8N Builder components
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from n8n_builder.error_handler import EnhancedErrorHandler, ErrorDetail
from n8n_builder.logging_config import get_logger


@dataclass
class DetectedError:
    """Represents a detected error with metadata."""
    error_id: str
    timestamp: datetime
    log_file: str
    line_number: int
    raw_message: str
    error_detail: ErrorDetail
    severity: str
    category: str
    frequency: int = 1
    first_seen: Optional[datetime] = None
    last_seen: Optional[datetime] = None


class LogFileHandler(FileSystemEventHandler):
    """Handles file system events for log files."""
    
    def __init__(self, error_monitor):
        self.error_monitor = error_monitor
        self.logger = get_logger('self_healer.log_handler')
    
    def on_modified(self, event):
        """Handle file modification events."""
        if not event.is_directory and event.src_path.endswith('.log'):
            # Schedule the async task safely from a thread
            try:
                loop = asyncio.get_running_loop()
                loop.call_soon_threadsafe(
                    lambda: asyncio.create_task(
                        self.error_monitor._process_log_file_change(event.src_path)
                    )
                )
            except RuntimeError:
                # No running event loop, queue the file for processing
                self.error_monitor._queue_log_file_change(event.src_path)


class ErrorMonitor:
    """
    Monitors error logs and system state for issues requiring healing.
    
    Features:
    - Real-time log file monitoring
    - Error classification and deduplication
    - Pattern recognition for recurring issues
    - Integration with existing error handling
    """
    
    def __init__(self, log_directory: Optional[Path] = None):
        """Initialize the Error Monitor."""
        self.logger = get_logger('self_healer.error_monitor')
        
        # Configuration
        self.log_directory = log_directory or Path(__file__).parent.parent.parent / "logs"
        self.error_log_path = self.log_directory / "errors.log"
        self.main_log_path = self.log_directory / "n8n_builder.log"
        
        # Error handling
        self.error_handler = EnhancedErrorHandler()
        
        # State tracking
        self.detected_errors: Dict[str, DetectedError] = {}
        self.processed_lines: Dict[str, int] = {}  # Track last processed line per file
        self.error_patterns: Dict[str, re.Pattern] = {}
        self.is_running = False
        
        # File system monitoring
        self.observer = Observer()
        self.file_handler = LogFileHandler(self)
        
        # Error processing queue
        self.error_queue: asyncio.Queue = asyncio.Queue()
        self.processing_task: Optional[asyncio.Task] = None

        # Pending file changes (for when no event loop is running)
        self.pending_file_changes: List[str] = []

        # Application monitoring configuration
        self.app_monitoring_config: Optional[Dict[str, Any]] = None
        self.app_monitoring_task: Optional[asyncio.Task] = None

        # Smart logging - track last status log time to reduce noise
        self.last_status_log_time = datetime.now()
        self.status_log_interval = 300  # 5 minutes in seconds

        # Load error patterns
        self._load_error_patterns()

        self.logger.info("Error Monitor initialized")
    
    def _load_error_patterns(self):
        """Load error detection patterns."""
        # Simple and reliable error patterns based on consistent log format
        # Format: [timestamp] ERROR module.name: Error message
        self.error_patterns = {
            'error_log_entry': re.compile(r'\[.*?\]\s+ERROR\s+([^:]+):\s*(.+)', re.IGNORECASE),
            'critical_log_entry': re.compile(r'\[.*?\]\s+CRITICAL\s+([^:]+):\s*(.+)', re.IGNORECASE),
            'fatal_log_entry': re.compile(r'\[.*?\]\s+FATAL\s+([^:]+):\s*(.+)', re.IGNORECASE),
            # Additional patterns for specific error types
            'validation_error': re.compile(r'\[.*?\]\s+ERROR\s+.*validation.*:\s*(.+)', re.IGNORECASE),
            'workflow_error': re.compile(r'\[.*?\]\s+ERROR\s+.*n8n_builder.*:\s*(.+)', re.IGNORECASE),
            'cache_error': re.compile(r'\[.*?\]\s+ERROR\s+.*cache.*:\s*(.+)', re.IGNORECASE),
            'websocket_error': re.compile(r'\[.*?\]\s+ERROR\s+.*dashboard.*:\s*(.+)', re.IGNORECASE)
        }

        self.logger.debug(f"Loaded {len(self.error_patterns)} error patterns")
    
    async def start(self):
        """Start the error monitoring system."""
        if self.is_running:
            self.logger.warning("Error Monitor is already running")
            return
        
        self.is_running = True
        
        # Ensure log directory exists
        self.log_directory.mkdir(exist_ok=True)
        
        # Initialize file tracking
        await self._initialize_file_tracking()

        # Process any pending file changes
        await self._process_pending_file_changes()

        # Start file system monitoring
        self.observer.schedule(self.file_handler, str(self.log_directory), recursive=False)
        self.observer.start()
        
        # Start error processing task
        self.processing_task = asyncio.create_task(self._process_error_queue())

        # Start application monitoring if configured
        if self.app_monitoring_config and not self.app_monitoring_task:
            self.app_monitoring_task = asyncio.create_task(self._application_monitoring_loop())

        # Initial scan of existing log files
        await self._scan_existing_logs()

        self.logger.info("Error Monitor started successfully")
    
    async def stop(self):
        """Stop the error monitoring system."""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # Stop file system monitoring
        self.observer.stop()
        self.observer.join()
        
        # Stop processing task
        if self.processing_task:
            self.processing_task.cancel()
            try:
                await self.processing_task
            except asyncio.CancelledError:
                pass

        # Stop application monitoring task
        if self.app_monitoring_task:
            self.app_monitoring_task.cancel()
            try:
                await self.app_monitoring_task
            except asyncio.CancelledError:
                pass

        self.logger.info("Error Monitor stopped")
    
    async def _initialize_file_tracking(self):
        """Initialize tracking of log files."""
        for log_file in [self.error_log_path, self.main_log_path]:
            if log_file.exists():
                # Start tracking from end of file to avoid processing old errors
                with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()
                    self.processed_lines[str(log_file)] = len(lines)
            else:
                self.processed_lines[str(log_file)] = 0
    
    async def _scan_existing_logs(self):
        """Scan existing log files for recent errors."""
        # Only scan recent entries (last hour) to avoid overwhelming the system
        cutoff_time = datetime.now() - timedelta(hours=1)
        
        for log_file in [self.error_log_path, self.main_log_path]:
            if log_file.exists():
                await self._process_log_file(str(log_file), recent_only=True, cutoff_time=cutoff_time)
    
    async def _process_log_file_change(self, file_path: str):
        """Process changes to a log file."""
        await self._process_log_file(file_path)

    def _queue_log_file_change(self, file_path: str):
        """Queue a log file change for later processing when no event loop is available."""
        if file_path not in self.pending_file_changes:
            self.pending_file_changes.append(file_path)

    async def _process_pending_file_changes(self):
        """Process any pending file changes that were queued."""
        if not self.pending_file_changes:
            return

        for file_path in self.pending_file_changes:
            try:
                await self._process_log_file_change(file_path)
            except Exception as e:
                self.logger.error(f"Error processing pending file change {file_path}: {e}")

        # Clear the pending changes
        self.pending_file_changes.clear()

    async def configure_application_monitoring(self, config: Dict[str, Any]):
        """Configure monitoring of an external application."""
        self.app_monitoring_config = config
        self.logger.info(f"Application monitoring configured for: {config.get('app_url', 'unknown')}")

        # Start application monitoring if we're already running
        if self.is_running and not self.app_monitoring_task:
            self.app_monitoring_task = asyncio.create_task(self._application_monitoring_loop())

    async def _application_monitoring_loop(self):
        """Monitor the external application for errors."""
        if not self.app_monitoring_config:
            return

        import aiohttp

        app_url = self.app_monitoring_config.get('app_url')
        health_endpoint = self.app_monitoring_config.get('health_endpoint')
        interval = self.app_monitoring_config.get('monitoring_interval', 30)

        self.logger.info(f"Starting application monitoring for {app_url}")

        async with aiohttp.ClientSession() as session:
            while self.is_running:
                try:
                    # Check application health
                    if health_endpoint:
                        await self._check_application_health(session, health_endpoint)

                    # Wait for next check
                    await asyncio.sleep(interval)

                except asyncio.CancelledError:
                    break
                except Exception as e:
                    self.logger.error(f"Error in application monitoring: {e}")
                    # Create an error for the monitoring failure itself
                    await self._create_application_error(
                        "Application Monitoring Error",
                        f"Failed to monitor application: {str(e)}",
                        "ERROR"
                    )
                    await asyncio.sleep(interval)

    async def _check_application_health(self, session: 'aiohttp.ClientSession', health_endpoint: str):
        """Check the health of the monitored application."""
        try:
            async with session.get(health_endpoint, timeout=10) as response:
                if response.status >= 400:
                    await self._create_application_error(
                        "Application Health Check Failed",
                        f"Health endpoint returned status {response.status}",
                        "ERROR" if response.status >= 500 else "WARNING"
                    )
                elif response.status == 200:
                    # Application is healthy - could log this for metrics
                    pass

        except asyncio.TimeoutError:
            await self._create_application_error(
                "Application Health Check Timeout",
                f"Health check timed out for {health_endpoint}",
                "WARNING"
            )
        except Exception as e:
            await self._create_application_error(
                "Application Health Check Error",
                f"Failed to check application health: {str(e)}",
                "ERROR"
            )

    async def _create_application_error(self, title: str, message: str, severity: str):
        """Create an error from application monitoring."""
        # Generate error ID based on content (similar to _parse_error_line)
        error_content = f"application_error:{title}:{message}"
        error_id = hashlib.md5(error_content.encode()).hexdigest()[:16]

        error_info = {
            'error_id': error_id,  # Add the missing error_id field
            'timestamp': datetime.now(),
            'error_message': message,
            'error_level': severity,
            'source_file': 'application_monitor',
            'line_number': 0,
            'context': f"Application monitoring detected: {title}",
            # Add missing fields that _process_detected_error expects
            'log_file': 'application_monitor',
            'raw_message': f"{title}: {message}",
            'pattern_name': 'application_error',
            'severity': severity
        }

        await self.error_queue.put(error_info)
        self.logger.warning(f"Application error detected: {title} - {message}")
    
    async def _process_log_file(self, file_path: str, recent_only: bool = False, cutoff_time: Optional[datetime] = None):
        """Process a log file for new errors."""
        try:
            if not Path(file_path).exists():
                return
            
            last_processed = self.processed_lines.get(file_path, 0)
            
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                
                # Process only new lines
                new_lines = lines[last_processed:]
                
                for i, line in enumerate(new_lines):
                    line_number = last_processed + i + 1
                    
                    # Extract timestamp if available
                    timestamp = self._extract_timestamp(line)
                    
                    # Skip old entries if recent_only is True
                    if recent_only and cutoff_time and timestamp and timestamp < cutoff_time:
                        continue
                    
                    # Check if line contains an error
                    # Use current time if timestamp extraction failed
                    error_timestamp = timestamp if timestamp is not None else datetime.now()
                    error_info = self._parse_error_line(line, file_path, line_number, error_timestamp)
                    if error_info:
                        await self.error_queue.put(error_info)
                
                # Update processed line count
                self.processed_lines[file_path] = len(lines)
                
        except Exception as e:
            self.logger.error(f"Error processing log file {file_path}: {e}")
    
    def _extract_timestamp(self, line: str) -> Optional[datetime]:
        """Extract timestamp from log line."""
        # Common timestamp patterns
        timestamp_patterns = [
            r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})',  # YYYY-MM-DD HH:MM:SS
            r'(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})',  # ISO format
        ]
        
        for pattern in timestamp_patterns:
            match = re.search(pattern, line)
            if match:
                try:
                    timestamp_str = match.group(1)
                    # Try different formats
                    for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%dT%H:%M:%S']:
                        try:
                            return datetime.strptime(timestamp_str, fmt)
                        except ValueError:
                            continue
                except Exception:
                    continue
        
        return datetime.now()  # Fallback to current time
    
    def _parse_error_line(self, line: str, file_path: str, line_number: int, timestamp: datetime) -> Optional[Dict[str, Any]]:
        """Parse a log line to detect errors."""
        line = line.strip()
        if not line:
            return None

        # Skip if this error has already been healed
        if self._is_error_already_healed(line):
            return None

        # Skip error handler's own categorization messages to prevent infinite loops
        if "Categorizing error:" in line and "n8n_builder.error_handler" in line:
            return None

        # Check each error pattern
        for pattern_name, pattern in self.error_patterns.items():
            match = pattern.search(line)
            if match:
                self.logger.debug(f"Pattern '{pattern_name}' matched line {line_number}: {line[:100]}...")

                if match.groups() and len(match.groups()) >= 2:
                    # New format: captures module and error message
                    module_name = match.group(1)
                    error_message = match.group(2)
                    full_error_message = f"{module_name}: {error_message}"
                else:
                    # Fallback for single capture group or no groups
                    error_message = match.group(1) if match.groups() else line.strip()
                    full_error_message = error_message

                # Create error ID based on content
                error_content = f"{pattern_name}:{full_error_message}"
                error_id = hashlib.md5(error_content.encode()).hexdigest()[:16]

                error_detail = {
                    'error_id': error_id,
                    'timestamp': timestamp,
                    'log_file': file_path,
                    'line_number': line_number,
                    'raw_message': line,
                    'error_message': full_error_message,
                    'pattern_name': pattern_name,
                    'severity': self._determine_severity(line, pattern_name)
                }

                self.logger.info(f"Detected error: {full_error_message} (pattern: {pattern_name})")
                return error_detail
        
        return None
    
    def _determine_severity(self, line: str, pattern_name: str) -> str:
        """Determine error severity based on content."""
        line_lower = line.lower()

        if 'critical' in line_lower or pattern_name == 'critical_log_entry':
            return 'CRITICAL'
        elif 'fatal' in line_lower or pattern_name == 'fatal_log_entry':
            return 'FATAL'
        elif 'error' in line_lower or pattern_name == 'error_log_entry':
            return 'ERROR'
        elif 'warning' in line_lower:
            return 'WARNING'
        else:
            return 'INFO'

    async def _process_error_queue(self):
        """Process detected errors from the queue."""
        while self.is_running:
            try:
                # Wait for new error with timeout
                error_info = await asyncio.wait_for(self.error_queue.get(), timeout=1.0)
                await self._process_detected_error(error_info)

            except asyncio.TimeoutError:
                continue  # Normal timeout, continue monitoring
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error processing error queue: {e}")
                await asyncio.sleep(1)

    async def _process_remaining_error_queue(self):
        """Process any remaining items in the error queue (used during forced rescans)."""
        processed_count = 0
        while not self.error_queue.empty():
            try:
                error_info = self.error_queue.get_nowait()
                await self._process_detected_error(error_info)
                processed_count += 1
            except asyncio.QueueEmpty:
                break
            except Exception as e:
                self.logger.error(f"Error processing remaining queue item: {e}")

        if processed_count > 0:
            self.logger.info(f"Processed {processed_count} remaining items from error queue")

    async def _process_detected_error(self, error_info: Dict[str, Any]):
        """Process a detected error and create ErrorDetail."""
        try:
            # Validate error_info structure
            if not isinstance(error_info, dict):
                self.logger.error(f"Invalid error_info type: {type(error_info)}, expected dict")
                return

            required_keys = ['error_id', 'timestamp', 'log_file', 'line_number', 'raw_message', 'error_message', 'pattern_name', 'severity']
            missing_keys = [key for key in required_keys if key not in error_info]
            if missing_keys:
                self.logger.error(f"Missing required keys in error_info: {missing_keys}. Available keys: {list(error_info.keys())}")
                return

            error_id = error_info['error_id']

            # Check if we've seen this error before
            if error_id in self.detected_errors:
                # Update existing error
                existing_error = self.detected_errors[error_id]
                existing_error.frequency += 1
                existing_error.last_seen = error_info['timestamp']

                # Only re-process if it's been a while since last occurrence
                if existing_error.last_seen:
                    time_since_last = datetime.now() - existing_error.last_seen
                    if time_since_last.total_seconds() < 300:  # 5 minutes cooldown
                        return
            else:
                # Create new error entry
                # Create a mock exception for error handler
                mock_exception = Exception(error_info['error_message'])

                # Use existing error handler to categorize
                error_detail = self.error_handler.categorize_error(
                    mock_exception,
                    {
                        'log_file': error_info['log_file'],
                        'line_number': error_info['line_number'],
                        'pattern': error_info['pattern_name'],
                        'raw_message': error_info['raw_message'],
                        'error_id': error_id,  # Store error_id in context
                        'detected_severity': error_info['severity']
                    }
                )

                detected_error = DetectedError(
                    error_id=error_id,
                    timestamp=error_info['timestamp'],
                    log_file=error_info['log_file'],
                    line_number=error_info['line_number'],
                    raw_message=error_info['raw_message'],
                    error_detail=error_detail,
                    severity=error_info['severity'],
                    category=error_info['pattern_name'],
                    first_seen=error_info['timestamp'],
                    last_seen=error_info['timestamp']
                )

                self.detected_errors[error_id] = detected_error

                self.logger.info(f"New error detected: {error_id} - {error_detail.title}")

        except KeyError as e:
            self.logger.error(f"Missing key in error_info: {e}. Available keys: {list(error_info.keys()) if isinstance(error_info, dict) else 'Not a dict'}")
        except Exception as e:
            self.logger.error(f"Error processing detected error: {e}. Error info: {error_info}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")

    async def get_new_errors(self) -> List[ErrorDetail]:
        """Get list of new errors that need healing attention."""
        new_errors = []
        current_time = datetime.now()

        self.logger.debug(f"Checking {len(self.detected_errors)} detected errors for healing criteria")

        for error_id, detected_error in self.detected_errors.items():
            # Consider error "new" if:
            # 1. It's recent (within last 2 hours) - relaxed from 1 hour
            # 2. It's recurring (frequency > 1 and recent)
            # 3. It's critical severity
            # 4. It's a validation error (high priority for healing)

            time_since_detection = current_time - detected_error.timestamp
            is_recent = time_since_detection.total_seconds() < 7200  # 2 hours (relaxed)
            is_recurring = detected_error.frequency > 1
            is_critical = detected_error.severity in ['CRITICAL', 'ERROR']
            is_validation_error = 'validation' in detected_error.category.lower()
            is_workflow_error = 'workflow' in detected_error.category.lower()

            # Check if error message contains validation-related keywords
            # Use both the processed message and raw message for keyword detection
            error_message = detected_error.error_detail.message.lower()
            raw_message = detected_error.raw_message.lower()
            combined_message = f"{error_message} {raw_message}"

            is_validation_in_message = any(keyword in combined_message for keyword in ['validation', 'trigger node', 'workflow failed'])

            # Check if it's a workflow generation error (high priority)
            is_workflow_generation_error = any(keyword in combined_message for keyword in ['generating workflow', 'workflow generation', 'extract valid json'])

            # More relaxed criteria for healing - prioritize validation and workflow errors
            # Validation errors and workflow generation errors are ALWAYS processed regardless of age
            meets_criteria = (
                (is_recent and is_critical) or
                (is_recurring and is_recent) or
                (is_validation_error) or  # Always process validation errors (regardless of age)
                (is_workflow_error) or    # Always process workflow errors (regardless of age)
                (is_validation_in_message) or  # Always process validation-related messages (regardless of age)
                (is_workflow_generation_error)  # Always process workflow generation errors (regardless of age)
            )

            if meets_criteria:
                new_errors.append(detected_error.error_detail)
                self.logger.info(f"✅ Error {error_id[:8]} meets healing criteria: recent={is_recent}, critical={is_critical}, validation={is_validation_error}, workflow={is_workflow_error}, validation_msg={is_validation_in_message}, workflow_gen={is_workflow_generation_error}")
            else:
                # Log first few errors that don't meet criteria for debugging
                if len(new_errors) < 3:
                    self.logger.info(f"❌ Error {error_id[:8]} ({detected_error.error_detail.title}) does not meet criteria:")
                    self.logger.info(f"   - Recent (< 2hrs): {is_recent} (age: {time_since_detection.total_seconds()/3600:.1f}h)")
                    self.logger.info(f"   - Critical: {is_critical} (severity: {detected_error.severity})")
                    self.logger.info(f"   - Validation category: {is_validation_error} (category: {detected_error.category})")
                    self.logger.info(f"   - Validation message: {is_validation_in_message}")
                    self.logger.info(f"   - Workflow generation: {is_workflow_generation_error}")
                    self.logger.info(f"   - Message: {detected_error.error_detail.message[:100]}...")

        # Smart logging: only log status every 5 minutes, or immediately if there are errors
        current_time = datetime.now()
        time_since_last_log = (current_time - self.last_status_log_time).total_seconds()

        should_log_status = (
            len(new_errors) > 0 or  # Always log when there are errors
            time_since_last_log >= self.status_log_interval  # Or every 5 minutes
        )

        if should_log_status:
            if len(new_errors) > 0:
                self.logger.info(f"🔍 Found {len(new_errors)} errors meeting healing criteria out of {len(self.detected_errors)} total errors")
            else:
                self.logger.info(f"📊 Status check: {len(self.detected_errors)} total errors monitored, none requiring immediate healing")
            self.last_status_log_time = current_time

        return new_errors

    def get_error_statistics(self) -> Dict[str, Any]:
        """Get statistics about detected errors."""
        total_errors = len(self.detected_errors)
        if total_errors == 0:
            return {
                'total_errors': 0,
                'by_severity': {},
                'by_category': {},
                'recent_errors': 0,
                'recurring_errors': 0
            }

        # Count by severity
        severity_counts = {}
        category_counts = {}
        recent_count = 0
        recurring_count = 0

        current_time = datetime.now()

        for detected_error in self.detected_errors.values():
            # Severity counts
            severity = detected_error.severity
            severity_counts[severity] = severity_counts.get(severity, 0) + 1

            # Category counts
            category = detected_error.category
            category_counts[category] = category_counts.get(category, 0) + 1

            # Recent errors (last hour)
            time_since = current_time - detected_error.timestamp
            if time_since.total_seconds() < 3600:
                recent_count += 1

            # Recurring errors
            if detected_error.frequency > 1:
                recurring_count += 1

        return {
            'total_errors': total_errors,
            'by_severity': severity_counts,
            'by_category': category_counts,
            'recent_errors': recent_count,
            'recurring_errors': recurring_count,
            'most_frequent': max(
                self.detected_errors.values(),
                key=lambda x: x.frequency
            ).error_id if self.detected_errors else None
        }

    def clear_old_errors(self, max_age_hours: int = 24):
        """Clear old errors from memory."""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)

        old_error_ids = [
            error_id for error_id, detected_error in self.detected_errors.items()
            if detected_error.timestamp < cutoff_time
        ]

        for error_id in old_error_ids:
            del self.detected_errors[error_id]

        if old_error_ids:
            self.logger.info(f"Cleared {len(old_error_ids)} old errors")

    async def force_rescan_logs(self, hours_back: int = 2):
        """Force a rescan of log files for recent errors."""
        self.logger.info(f"Forcing rescan of logs for the last {hours_back} hours")

        # Reset processed line tracking to force reprocessing
        original_processed_lines = self.processed_lines.copy()
        errors_before = len(self.detected_errors)

        try:
            # Temporarily reset processed lines to scan from earlier point
            cutoff_time = datetime.now() - timedelta(hours=hours_back)
            self.logger.info(f"Scanning for errors since: {cutoff_time}")

            for log_file in [self.error_log_path, self.main_log_path]:
                if log_file.exists():
                    self.logger.info(f"Processing log file: {log_file}")

                    # Find the line number corresponding to the cutoff time
                    with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                        lines = f.readlines()

                    self.logger.info(f"Log file has {len(lines)} total lines")

                    # Find the first line after cutoff_time
                    start_line = 0
                    for i, line in enumerate(lines):
                        timestamp = self._extract_timestamp(line)
                        if timestamp and timestamp >= cutoff_time:
                            start_line = i
                            break

                    self.logger.info(f"Starting scan from line {start_line}")

                    # Set processed lines to start from that point
                    self.processed_lines[str(log_file)] = start_line

                    # Process the file
                    await self._process_log_file(str(log_file), recent_only=False)

            # Process any remaining items in the error queue
            await self._process_remaining_error_queue()

            errors_after = len(self.detected_errors)
            new_errors_found = errors_after - errors_before

            self.logger.info(f"Completed forced rescan - found {new_errors_found} new errors ({errors_after} total errors)")

            # Return the count of new errors found
            return new_errors_found

        except Exception as e:
            self.logger.error(f"Error during forced rescan: {e}")
            # Restore original processed lines on error
            self.processed_lines = original_processed_lines
            return 0

    async def log_healing_action(self, error_detail, action_taken: str, success: bool = True):
        """Log a healing action to the main log file for tracking."""
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S,%f")[:-3]  # Match log format
            status = "SelfHealed" if success else "SelfHealFailed"

            # Create a concise one-sentence summary
            summary = action_taken[:100] + "..." if len(action_taken) > 100 else action_taken

            log_entry = f"[{timestamp}] INFO self_healer.action: {status} - {error_detail.title}: {summary}\n"

            # Append to the main log file
            with open(self.main_log_path, 'a', encoding='utf-8') as f:
                f.write(log_entry)

            self.logger.info(f"Logged healing action: {status} - {error_detail.title}")

        except Exception as e:
            self.logger.error(f"Failed to log healing action: {e}")

    def _is_error_already_healed(self, line: str) -> bool:
        """Check if an error has already been healed by looking for SelfHealed markers."""
        # Look for SelfHealed marker in the same log context
        # This is a simple check - in practice, you might want more sophisticated tracking
        return "SelfHealed" in line or "SelfHealFailed" in line

    def get_healing_statistics_from_log(self, hours_back: int = 24) -> Dict[str, Any]:
        """Get healing statistics by analyzing the log file."""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours_back)
            healing_actions = []

            if not self.main_log_path.exists():
                return {"error": "Log file not found"}

            with open(self.main_log_path, 'r', encoding='utf-8', errors='ignore') as f:
                for line in f:
                    if "self_healer.action:" in line and ("SelfHealed" in line or "SelfHealFailed" in line):
                        timestamp = self._extract_timestamp(line)
                        if timestamp and timestamp >= cutoff_time:
                            healing_actions.append({
                                'timestamp': timestamp.isoformat(),
                                'success': "SelfHealed" in line,
                                'action': line.strip()
                            })

            total_actions = len(healing_actions)
            successful_actions = sum(1 for action in healing_actions if action['success'])
            failed_actions = total_actions - successful_actions

            return {
                'total_healing_actions': total_actions,
                'successful_healings': successful_actions,
                'failed_healings': failed_actions,
                'success_rate': (successful_actions / total_actions * 100) if total_actions > 0 else 0,
                'recent_actions': healing_actions[-10:],  # Last 10 actions
                'hours_analyzed': hours_back
            }

        except Exception as e:
            self.logger.error(f"Error getting healing statistics from log: {e}")
            return {"error": str(e)}

    def get_error_details(self, error_id: str) -> Optional[DetectedError]:
        """Get detailed information about a specific error."""
        return self.detected_errors.get(error_id)
