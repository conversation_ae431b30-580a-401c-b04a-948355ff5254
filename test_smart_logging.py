#!/usr/bin/env python3
"""
Test script to verify smart logging implementation in Self-Healer Error Monitor.
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from Self_Healer.core.error_monitor import ErrorMonitor
    print("✅ Successfully imported ErrorMonitor with smart logging")
    
    # Create an instance to test initialization
    monitor = ErrorMonitor()
    print(f"✅ ErrorMonitor initialized successfully")
    print(f"📊 Status log interval: {monitor.status_log_interval} seconds")
    print(f"⏰ Last status log time: {monitor.last_status_log_time}")
    
    # Test the get_new_errors method (should not log immediately since no errors)
    print("\n🧪 Testing smart logging behavior...")
    
    async def test_logging():
        # First call - should log since it's the first time
        errors1 = await monitor.get_new_errors()
        print(f"First call: Found {len(errors1)} errors")
        
        # Second call immediately - should NOT log (no errors, within 5 min interval)
        errors2 = await monitor.get_new_errors()
        print(f"Second call: Found {len(errors2)} errors (should not log status)")
        
        print("✅ Smart logging test completed")
    
    # Run the async test
    asyncio.run(test_logging())
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("This might be due to missing dependencies or path issues")
except Exception as e:
    print(f"❌ Error testing smart logging: {e}")
    import traceback
    traceback.print_exc()

print("\n🎉 Smart logging implementation test complete!")
